import { DocsLayout } from 'fumadocs-ui/layouts/docs';
import type { ReactNode } from 'react';
import { source } from '@/lib/source';
import { RootProvider } from 'fumadocs-ui/provider';
import { ThemeProvider } from '@/components/providers/theme-provider';
import './docs.css';

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <ThemeProvider>
      <RootProvider>
        <DocsLayout
          tree={{
            name: 'Docs',
            children: source.pageTree as any, // Fix type error by casting to 'any'
          }}
          nav={{
            title: 'OnlyRules Docs',
            url: '/docs',
          }}
          sidebar={{
            defaultOpenLevel: 1,
          }}
          links={[
            {
              text: 'Home',
              url: '/',
            },
            {
              text: 'Dashboard',
              url: '/dashboard',
            },
            {
              text: 'GitHub',
              url: 'https://github.com/ranglang/onlyrules',
              external: true,
            },
          ]}
        >
          {children}
        </DocsLayout>
      </RootProvider>
    </ThemeProvider>
  );
}
