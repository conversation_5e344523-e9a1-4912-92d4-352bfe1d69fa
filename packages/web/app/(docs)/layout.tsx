import { source } from '@/lib/source';
import { DocsLayout } from 'fumadocs-ui/layouts/docs';
import { DocsPage, DocsBody } from 'fumadocs-ui/page';
import { RootProvider } from 'fumadocs-ui/provider';
import { notFound } from 'next/navigation';
import type { Metadata, Viewport } from 'next';
import { JotaiProvider } from '@/components/providers/jotai-provider';
import { QueryProvider } from '@/components/providers/query-provider';
import { ToasterProvider } from '@/components/providers/toaster-provider';

export const dynamic = 'force-dynamic';
export const revalidate = 0;
export const runtime = 'nodejs';

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  title: 'OnlyRules - AI Prompt Management Platform',
  description: 'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',
  keywords: 'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Ki<PERSON>, Tencent Cloud CodeBuddy',
  authors: [{ name: 'OnlyRules Team' }],
  publisher: 'OnlyRules',
  category: 'Technology',
  classification: 'Software Development Tools',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
    type: 'website',
    locale: 'en_US',
    siteName: 'OnlyRules',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'hsl(0 0% 100%)' },
    { media: '(prefers-color-scheme: dark)', color: 'hsl(240 10% 3.9%)' },
  ],
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body suppressHydrationWarning>
          <JotaiProvider>
            <QueryProvider>

      <RootProvider>
        <DocsLayout
          tree={{
            name: 'Docs',
            children: source.pageTree as any, // Fix type error by casting to 'any'
          }}
          nav={{
            title: 'OnlyRules Docs',
            url: '/docs',
          }}
          sidebar={{
            defaultOpenLevel: 1,
          }}
          links={[
            {
              text: 'Home',
              url: '/',
            },
            {
              text: 'Dashboard',
              url: '/dashboard',
            },
            {
              text: 'GitHub',
              url: 'https://github.com/ranglang/onlyrules',
              external: true,
            },
          ]}
        >
          {children}
        </DocsLayout>
      </RootProvider>
              <ToasterProvider />
            </QueryProvider>
          </JotaiProvider>
      </body>
    </html>
  );
}