"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/auth/signin/page",{

/***/ "(app-pages-browser)/./app/(main)/auth/signin/page.tsx":
/*!*****************************************!*\
  !*** ./app/(main)/auth/signin/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/themes */ \"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/card.js\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/themes */ \"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/themes */ \"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/button.js\");\n/* harmony import */ var _lib_auth_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-client */ \"(app-pages-browser)/./lib/auth-client.ts\");\n/* harmony import */ var _barrel_optimize_names_Code_Github_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Github!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Github_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Github!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \nvar _s = $RefreshSig$();\n// Force dynamic rendering for this page\nconst dynamic = 'force-dynamic';\n\n\n\n\n\n\nfunction SignInPage() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleGitHubSignIn = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const result = await _lib_auth_client__WEBPACK_IMPORTED_MODULE_4__.signIn.social({\n                provider: \"github\",\n                callbackURL: \"/dashboard\"\n            });\n            if (result.error) {\n                setError(result.error.message || \"Failed to sign in with GitHub\");\n            }\n        } catch (err) {\n            setError(\"An unexpected error occurred\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-muted/50 py-8 xs:py-12 mobile-padding\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md space-y-6 xs:space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/\",\n                        className: \"inline-flex items-center gap-2 mb-6 xs:mb-8 touch-target\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Github_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-6 w-6 xs:h-8 xs:w-8 text-primary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-bold text-xl xs:text-2xl\",\n                                children: \"OnlyRules\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"space-y-4 xs:space-y-6 mobile-card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xl xs:text-2xl text-center font-semibold\",\n                                    children: \"Welcome back\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-muted-foreground text-sm xs:text-base\",\n                                    children: \"Sign in with your GitHub account to continue\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 xs:space-y-6\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_7__.Root, {\n                                    color: \"red\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        className: \"text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    onClick: handleGitHubSignIn,\n                                    className: \"w-full touch-target\",\n                                    disabled: isLoading,\n                                    size: \"3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Github_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 xs:h-5 xs:w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm xs:text-base\",\n                                            children: isLoading ? \"Signing in...\" : \"Continue with GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-xs xs:text-sm text-muted-foreground leading-relaxed px-2\",\n                                    children: \"By signing in, you agree to our terms of service and privacy policy.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPage, \"wRkgLzWz2o3RW6JsVbGLt3B42As=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SignInPage;\nvar _c;\n$RefreshReg$(_c, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/auth/signin/page.tsx\n"));

/***/ })

});