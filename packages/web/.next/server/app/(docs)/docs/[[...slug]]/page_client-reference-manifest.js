globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(docs)/docs/[[...slug]]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./app/global-error.tsx":{"*":{"id":"(ssr)/./app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-accordion/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-accordion/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-collapsible/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-collapsible/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-context-menu/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-context-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-dialog/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-form/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-form/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-hover-card/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-hover-card/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-label/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-menubar/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-menubar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-popover/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-popover/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-portal/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-portal/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-progress/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-progress/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-radio-group/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-radio-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-select/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-select/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-slider/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-slider/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-switch/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-switch/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toast/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle-group/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toggle-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toggle/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toolbar/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toolbar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-tooltip/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-tooltip/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/avatar.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/avatar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/context-menu.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/context-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio-group.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/radio-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/radio.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/select.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/select.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/text-field.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/text-field.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/client-navbar.tsx":{"*":{"id":"(ssr)/./components/layout/client-navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/jotai-provider.tsx":{"*":{"id":"(ssr)/./components/providers/jotai-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/query-provider.tsx":{"*":{"id":"(ssr)/./components/providers/query-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/theme-provider.tsx":{"*":{"id":"(ssr)/./components/providers/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/toaster-provider.tsx":{"*":{"id":"(ssr)/./components/providers/toaster-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/error.tsx":{"*":{"id":"(ssr)/./app/(main)/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/test-theme/page.tsx":{"*":{"id":"(ssr)/./app/(main)/test-theme/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/auth/signin/page.tsx":{"*":{"id":"(ssr)/./app/(main)/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-core/dist/link.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-core/dist/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-core/dist/hide-if-empty.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-core/dist/hide-if-empty.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/layout.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/tree.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/tree.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/links.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/links.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/provider/index.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/provider/index.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx":{"id":"(app-pages-browser)/./app/global-error.tsx","name":"*","chunks":["app/global-error","static/chunks/app/global-error.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-accordion/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-accordion/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-avatar/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-checkbox/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-collapsible/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-collapsible/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-context-menu/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-context-menu/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-dialog/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-dialog/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-form/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-form/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-hover-card/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-hover-card/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-label/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-menubar/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-menubar/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-popover/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-popover/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-portal/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-portal/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-progress/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-progress/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-radio-group/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-radio-group/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-scroll-area/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-select/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-select/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-slider/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-slider/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-switch/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-switch/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-tabs/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-toast/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-toast/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-toggle-group/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle-group/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-toggle/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-toolbar/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-toolbar/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/react-tooltip/dist/index.mjs":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/react-tooltip/dist/index.mjs","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/avatar.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/avatar.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/callout.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/checkbox.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/context-menu.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/context-menu.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/radio-group.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio-group.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/radio.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/select.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/select.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/text-field.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/text-field.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/@radix-ui/themes/dist/esm/components/theme.js":{"id":"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/app-dir/link.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/app-dir/link.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/layout/client-navbar.tsx":{"id":"(app-pages-browser)/./components/layout/client-navbar.tsx","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/jotai-provider.tsx":{"id":"(app-pages-browser)/./components/providers/jotai-provider.tsx","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/query-provider.tsx":{"id":"(app-pages-browser)/./components/providers/query-provider.tsx","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx":{"id":"(app-pages-browser)/./components/providers/theme-provider.tsx","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/toaster-provider.tsx":{"id":"(app-pages-browser)/./components/providers/toaster-provider.tsx","name":"*","chunks":["app/(docs)/layout","static/chunks/app/(docs)/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/error.tsx":{"id":"(app-pages-browser)/./app/(main)/error.tsx","name":"*","chunks":["app/(main)/error","static/chunks/app/(main)/error.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/test-theme/page.tsx":{"id":"(app-pages-browser)/./app/(main)/test-theme/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/lib/metadata/generate/icon-mark.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/next/dist/esm/next-devtools/userspace/app/segment-explorer-node.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/auth/signin/page.tsx":{"id":"(app-pages-browser)/./app/(main)/auth/signin/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/link.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-core/dist/link.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js","name":"*","chunks":["app/(docs)/docs/[[...slug]]/page","static/chunks/app/(docs)/docs/%5B%5B...slug%5D%5D/page.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/toc.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js","name":"*","chunks":["app/(docs)/docs/[[...slug]]/page","static/chunks/app/(docs)/docs/%5B%5B...slug%5D%5D/page.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/i18n.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js","name":"*","chunks":["app/(docs)/docs/[[...slug]]/page","static/chunks/app/(docs)/docs/%5B%5B...slug%5D%5D/page.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","name":"*","chunks":["app/(docs)/docs/[[...slug]]/page","static/chunks/app/(docs)/docs/%5B%5B...slug%5D%5D/page.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/hide-if-empty.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-core/dist/hide-if-empty.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/language-toggle.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/root-toggle.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/search-toggle.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/sidebar.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/layout.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/layout.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/contexts/tree.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/tree.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/docs-client.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/layouts/links.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/links.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-ui/dist/provider/index.js":{"id":"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/provider/index.js","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/docs.css":{"id":"(app-pages-browser)/./app/(docs)/docs/docs.css","name":"*","chunks":["app/(docs)/docs/layout","static/chunks/app/(docs)/docs/layout.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/codespace/onlyrules-website/project/packages/web/":[],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error":[],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/layout":[{"inlined":false,"path":"static/css/app/(main)/layout.css"}],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/error":[],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/not-found":[],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/layout":[],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/page":[],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout":[{"inlined":false,"path":"static/css/app/(docs)/layout.css"}],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page":[],"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/layout":[{"inlined":false,"path":"static/css/app/(docs)/docs/layout.css"}]},"rscModuleMapping":{"(app-pages-browser)/./app/global-error.tsx":{"*":{"id":"(rsc)/./app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-accordion/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-accordion/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-collapsible/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-collapsible/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-context-menu/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-context-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-dialog/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-form/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-form/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-hover-card/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-hover-card/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-label/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-menubar/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-menubar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-popover/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-popover/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-portal/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-portal/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-progress/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-progress/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-radio-group/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-radio-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-select/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-select/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-slider/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-slider/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-switch/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-switch/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toast/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle-group/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-toggle-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-toggle/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toolbar/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-toolbar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-tooltip/dist/index.mjs":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/react-tooltip/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/avatar.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/avatar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/context-menu.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/context-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio-group.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/radio-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/radio.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/select.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/select.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/text-field.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/text-field.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js":{"*":{"id":"(rsc)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/client-navbar.tsx":{"*":{"id":"(rsc)/./components/layout/client-navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/jotai-provider.tsx":{"*":{"id":"(rsc)/./components/providers/jotai-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/query-provider.tsx":{"*":{"id":"(rsc)/./components/providers/query-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/theme-provider.tsx":{"*":{"id":"(rsc)/./components/providers/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/toaster-provider.tsx":{"*":{"id":"(rsc)/./components/providers/toaster-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/error.tsx":{"*":{"id":"(rsc)/./app/(main)/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/test-theme/page.tsx":{"*":{"id":"(rsc)/./app/(main)/test-theme/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/auth/signin/page.tsx":{"*":{"id":"(rsc)/./app/(main)/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-core/dist/link.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-core/dist/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-core/dist/hide-if-empty.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-core/dist/hide-if-empty.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/layout.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/contexts/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/tree.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/contexts/tree.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/links.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/layouts/links.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/provider/index.js":{"*":{"id":"(rsc)/../../node_modules/fumadocs-ui/dist/provider/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(docs)/docs/docs.css":{"*":{"id":"(rsc)/./app/(docs)/docs/docs.css","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"(app-pages-browser)/../../node_modules/@radix-ui/react-accordion/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-accordion/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-collapsible/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-collapsible/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-context-menu/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-context-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-dialog/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-dialog/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-form/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-form/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-hover-card/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-hover-card/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-label/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-menubar/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-menubar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-one-time-password-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-password-toggle-field/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-popover/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-popover/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-portal/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-portal/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-progress/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-progress/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-radio-group/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-radio-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-select/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-select/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-slider/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-slider/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-switch/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-switch/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toast/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle-group/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toggle-group/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toggle/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toggle/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-toolbar/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-toolbar/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/react-tooltip/dist/index.mjs":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/react-tooltip/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/avatar.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/avatar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/callout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-cards.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/checkbox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/context-menu.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/context-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/dropdown-menu.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio-group.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/radio-group.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/radio.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/radio.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/segmented-control.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/select.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/select.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/text-field.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/text-field.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/theme-panel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js":{"*":{"id":"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-core/dist/link.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-core/dist/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-core/dist/hide-if-empty.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-core/dist/hide-if-empty.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/layout.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/contexts/tree.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/tree.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/layouts/links.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/links.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/fumadocs-ui/dist/provider/index.js":{"*":{"id":"(ssr)/../../node_modules/fumadocs-ui/dist/provider/index.js","name":"*","chunks":[],"async":false}}}}